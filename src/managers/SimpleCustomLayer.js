/**
 * 简化的3D自定义图层管理器
 * 避免THREE.js的复杂性，使用基本的WebGL API
 */

class SimpleCustomLayer {
  constructor(options = {}) {
    this.options = {
      center: [101.5, 25.0], // 渲染中心点
      ...options
    }
    
    this.map = null
    this.customCoords = null
    this.glLayer = null
    this.isInitialized = false
    this.isVisible = true
    
    // WebGL相关
    this.gl = null
    this.program = null
    this.buffer = null
    this.animationTime = 0
  }

  /**
   * 初始化图层
   */
  async init(map) {
    try {
      console.log('开始初始化简化3D自定义图层...')
      
      if (!map) {
        throw new Error('地图实例不能为空')
      }

      this.map = map

      // 获取自定义坐标系统
      this.customCoords = this.map.customCoords
      if (!this.customCoords) {
        console.warn('无法获取地图的customCoords对象')
        return false
      }

      // 创建GLCustomLayer
      const self = this
      this.glLayer = new AMap.GLCustomLayer({
        zIndex: 10,
        init: (gl) => {
          console.log('GLCustomLayer init 开始')
          self.gl = gl
          self.initWebGL()
          console.log('GLCustomLayer init 完成')
        },
        render: () => {
          self.render()
        }
      })

      // 添加到地图
      this.map.add(this.glLayer)
      this.isInitialized = true
      
      console.log('简化3D自定义图层初始化成功')
      return true
    } catch (error) {
      console.error('简化3D自定义图层初始化失败:', error)
      return false
    }
  }

  /**
   * 初始化WebGL
   */
  initWebGL() {
    const gl = this.gl
    
    // 顶点着色器源码
    const vertexShaderSource = `
      attribute vec3 a_position;
      attribute vec3 a_color;
      uniform mat4 u_matrix;
      varying vec3 v_color;
      
      void main() {
        gl_Position = u_matrix * vec4(a_position, 1.0);
        v_color = a_color;
      }
    `
    
    // 片段着色器源码
    const fragmentShaderSource = `
      precision mediump float;
      varying vec3 v_color;
      
      void main() {
        gl_FragColor = vec4(v_color, 0.8);
      }
    `
    
    // 创建着色器程序
    this.program = this.createShaderProgram(gl, vertexShaderSource, fragmentShaderSource)
    
    // 创建立方体数据
    this.createCubeData()
    
    console.log('WebGL初始化完成')
  }

  /**
   * 创建着色器程序
   */
  createShaderProgram(gl, vertexSource, fragmentSource) {
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexSource)
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentSource)
    
    const program = gl.createProgram()
    gl.attachShader(program, vertexShader)
    gl.attachShader(program, fragmentShader)
    gl.linkProgram(program)
    
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('着色器程序链接失败:', gl.getProgramInfoLog(program))
      return null
    }
    
    return program
  }

  /**
   * 创建着色器
   */
  createShader(gl, type, source) {
    const shader = gl.createShader(type)
    gl.shaderSource(shader, source)
    gl.compileShader(shader)
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('着色器编译失败:', gl.getShaderInfoLog(shader))
      gl.deleteShader(shader)
      return null
    }
    
    return shader
  }

  /**
   * 创建立方体数据
   */
  createCubeData() {
    const gl = this.gl
    
    // 转换坐标
    const position = [101.5, 25.0] // 云南省中心
    const coords = this.customCoords.lngLatsToCoords([position])
    const coord = coords[0]
    
    // 立方体顶点数据（位置 + 颜色）
    const size = 2000
    const height = 2000
    const vertices = new Float32Array([
      // 前面 (红色)
      coord[0] - size, coord[1] - size, height + size,  1.0, 0.0, 0.0,
      coord[0] + size, coord[1] - size, height + size,  1.0, 0.0, 0.0,
      coord[0] + size, coord[1] + size, height + size,  1.0, 0.0, 0.0,
      coord[0] - size, coord[1] + size, height + size,  1.0, 0.0, 0.0,
      
      // 后面 (绿色)
      coord[0] - size, coord[1] - size, height - size,  0.0, 1.0, 0.0,
      coord[0] + size, coord[1] - size, height - size,  0.0, 1.0, 0.0,
      coord[0] + size, coord[1] + size, height - size,  0.0, 1.0, 0.0,
      coord[0] - size, coord[1] + size, height - size,  0.0, 1.0, 0.0,
    ])
    
    // 立方体索引
    const indices = new Uint16Array([
      0, 1, 2,  0, 2, 3,  // 前面
      4, 5, 6,  4, 6, 7,  // 后面
      0, 1, 5,  0, 5, 4,  // 底面
      2, 3, 7,  2, 7, 6,  // 顶面
      0, 3, 7,  0, 7, 4,  // 左面
      1, 2, 6,  1, 6, 5   // 右面
    ])
    
    // 创建缓冲区
    this.vertexBuffer = gl.createBuffer()
    gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer)
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW)
    
    this.indexBuffer = gl.createBuffer()
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer)
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW)
    
    this.indexCount = indices.length
    
    console.log('立方体数据创建完成')
  }

  /**
   * 渲染函数
   */
  render() {
    try {
      if (!this.gl || !this.program || !this.isVisible) return
      
      const gl = this.gl
      
      // 设置渲染中心点
      this.customCoords.setCenter(this.options.center)
      
      // 获取相机参数
      const { near, far, fov, up, lookAt, position } = this.customCoords.getCameraParams()
      
      // 创建变换矩阵（简化版本）
      const matrix = this.createMatrix(position, lookAt, up, fov, near, far)
      
      // 使用着色器程序
      gl.useProgram(this.program)
      
      // 设置顶点属性
      gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer)
      
      const positionLocation = gl.getAttribLocation(this.program, 'a_position')
      const colorLocation = gl.getAttribLocation(this.program, 'a_color')
      
      gl.enableVertexAttribArray(positionLocation)
      gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 24, 0)
      
      gl.enableVertexAttribArray(colorLocation)
      gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 24, 12)
      
      // 设置变换矩阵
      const matrixLocation = gl.getUniformLocation(this.program, 'u_matrix')
      gl.uniformMatrix4fv(matrixLocation, false, matrix)
      
      // 绑定索引缓冲区
      gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer)
      
      // 启用深度测试和混合
      gl.enable(gl.DEPTH_TEST)
      gl.enable(gl.BLEND)
      gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA)
      
      // 绘制立方体
      gl.drawElements(gl.TRIANGLES, this.indexCount, gl.UNSIGNED_SHORT, 0)
      
      // 更新动画时间
      this.animationTime += 0.01
      
    } catch (error) {
      console.error('简化渲染错误:', error)
    }
  }

  /**
   * 创建简化的变换矩阵
   */
  createMatrix(position, lookAt, up, fov, near, far) {
    // 这里应该创建正确的MVP矩阵，但为了简化，我们使用单位矩阵
    return new Float32Array([
      1, 0, 0, 0,
      0, 1, 0, 0,
      0, 0, 1, 0,
      0, 0, 0, 1
    ])
  }

  /**
   * 显示图层
   */
  show() {
    this.isVisible = true
    if (this.glLayer) {
      this.glLayer.show()
    }
  }

  /**
   * 隐藏图层
   */
  hide() {
    this.isVisible = false
    if (this.glLayer) {
      this.glLayer.hide()
    }
  }

  /**
   * 销毁图层
   */
  destroy() {
    if (this.glLayer && this.map) {
      this.map.remove(this.glLayer)
    }
    
    if (this.gl) {
      // 清理WebGL资源
      if (this.vertexBuffer) this.gl.deleteBuffer(this.vertexBuffer)
      if (this.indexBuffer) this.gl.deleteBuffer(this.indexBuffer)
      if (this.program) this.gl.deleteProgram(this.program)
    }
    
    this.isInitialized = false
    console.log('简化3D自定义图层已销毁')
  }

  /**
   * 获取图层状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isVisible: this.isVisible,
      hasWebGL: !!this.gl,
      hasProgram: !!this.program
    }
  }
}

export default SimpleCustomLayer
