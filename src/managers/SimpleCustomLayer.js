/**
 * 基于THREE.js的3D自定义图层管理器
 * 参考高德地图官方示例实现
 */

class SimpleCustomLayer {
  constructor(options = {}) {
    this.options = {
      center: [101.5, 25.0], // 渲染中心点
      ...options
    }

    this.map = null
    this.customCoords = null
    this.glLayer = null
    this.isInitialized = false
    this.isVisible = true

    // THREE.js相关
    this.renderer = null
    this.scene = null
    this.camera = null
    this.cube = null
    this.animationTime = 0
  }

  /**
   * 初始化图层
   */
  async init(map) {
    try {
      console.log('开始初始化简化3D自定义图层...')
      
      if (!map) {
        throw new Error('地图实例不能为空')
      }

      this.map = map

      // 获取自定义坐标系统
      this.customCoords = this.map.customCoords
      if (!this.customCoords) {
        console.warn('无法获取地图的customCoords对象')
        return false
      }

      // 创建GLCustomLayer
      const self = this
      this.glLayer = new AMap.GLCustomLayer({
        zIndex: 10,
        init: (gl) => {
          console.log('GLCustomLayer init 开始')
          self.initTHREE(gl)
          console.log('GLCustomLayer init 完成')
        },
        render: () => {
          self.render()
        }
      })

      // 添加到地图
      this.map.add(this.glLayer)
      this.isInitialized = true
      
      console.log('简化3D自定义图层初始化成功')
      return true
    } catch (error) {
      console.error('简化3D自定义图层初始化失败:', error)
      return false
    }
  }

  /**
   * 初始化THREE.js
   */
  initTHREE(gl) {
    // 获取自定义坐标系统
    this.customCoords = this.map.customCoords
    if (!this.customCoords) {
      console.error('无法获取customCoords')
      return
    }

    // 创建THREE.js渲染器，使用地图的WebGL上下文
    this.renderer = new THREE.WebGLRenderer({
      context: gl,
      antialias: true,
      alpha: true,
      premultipliedAlpha: false,
      stencil: false,
      preserveDrawingBuffer: false
    })

    // 重要：禁用自动清除，让地图背景显示
    this.renderer.autoClear = false
    this.renderer.sortObjects = false

    // 创建场景
    this.scene = new THREE.Scene()

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(60, 1, 1, 1000000)

    // 创建立方体
    this.createCube()

    console.log('THREE.js初始化完成')
  }

  /**
   * 创建立方体
   */
  createCube() {
    // 立方体几何体
    const geometry = new THREE.BoxGeometry(1000, 1000, 1000)

    // 立方体材质
    const material = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      transparent: true,
      opacity: 0.8
    })

    // 创建立方体网格
    this.cube = new THREE.Mesh(geometry, material)

    // 将立方体添加到场景
    this.scene.add(this.cube)

    // 设置立方体位置（使用地图坐标）
    const position = [101.5, 25.0] // 云南省中心
    const coords = this.customCoords.lngLatsToCoords([position])
    if (coords && coords.length > 0) {
      const coord = coords[0]
      this.cube.position.set(coord[0], coord[1], 1000)
      console.log('立方体位置设置为:', coord[0], coord[1], 1000)
    }
  }

  /**
   * 渲染函数 - 每帧调用
   */
  render() {
    if (!this.renderer || !this.scene || !this.camera || !this.cube) {
      return
    }

    try {
      // 设置渲染中心点
      this.customCoords.setCenter(this.options.center)

      // 获取相机参数
      const { near, far, fov, up, lookAt, position } = this.customCoords.getCameraParams()

      // 同步相机参数
      this.camera.near = near
      this.camera.far = far
      this.camera.fov = fov
      this.camera.position.set(position[0], position[1], position[2])
      this.camera.up.set(up[0], up[1], up[2])
      this.camera.lookAt(lookAt[0], lookAt[1], lookAt[2])
      this.camera.updateProjectionMatrix()

      // 添加旋转动画
      this.cube.rotation.x += 0.01
      this.cube.rotation.y += 0.01

      // 重置WebGL状态
      this.renderer.resetState()

      // 渲染场景
      this.renderer.render(this.scene, this.camera)

      // 再次重置状态
      this.renderer.resetState()

    } catch (error) {
      console.error('渲染错误:', error)
    }
  }



  /**
   * 显示图层
   */
  show() {
    this.isVisible = true
    if (this.glLayer) {
      this.glLayer.show()
    }
  }

  /**
   * 隐藏图层
   */
  hide() {
    this.isVisible = false
    if (this.glLayer) {
      this.glLayer.hide()
    }
  }

  /**
   * 销毁图层
   */
  destroy() {
    if (this.glLayer && this.map) {
      this.map.remove(this.glLayer)
    }

    if (this.renderer) {
      this.renderer.dispose()
    }

    if (this.scene) {
      // 清理场景中的对象
      while (this.scene.children.length > 0) {
        const child = this.scene.children[0]
        this.scene.remove(child)
        if (child.geometry) child.geometry.dispose()
        if (child.material) child.material.dispose()
      }
    }

    this.isInitialized = false
    console.log('3D自定义图层已销毁')
  }

  /**
   * 获取图层状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isVisible: this.isVisible,
      hasRenderer: !!this.renderer,
      hasScene: !!this.scene,
      hasCube: !!this.cube
    }
  }
}

export default SimpleCustomLayer
