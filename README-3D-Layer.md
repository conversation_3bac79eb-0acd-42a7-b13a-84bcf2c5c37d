# THREE.js 3D自定义图层功能使用指南

## 🎯 功能简介

已成功为云南省地图项目集成了基于THREE.js的3D自定义图层功能！这个功能参考了高德地图官方示例，使用THREE.js 0.142版本在3D地图模式下渲染各种3D模型和动画效果。

## 🚀 快速开始

### 1. 启动项目
```bash
npm run dev
```

### 2. 访问应用
打开浏览器访问：http://localhost:5173

### 3. 使用步骤
1. **切换到3D模式**：点击地图控制面板中的"3D地图"按钮
2. **启用3D图层**：在3D模式下，地图显示区域会出现"3D模型图层"开关
3. **查看效果**：开启开关后即可看到5个不同的3D模型在地图上显示

## 🎨 当前效果

### 3D模型展示
- **立方体**（红色）- 位于昆明附近，带旋转动画
- **球体**（青色）- 位于中心点右侧，带浮动效果
- **圆锥**（蓝色）- 位于中心点左下，带旋转动画
- **圆柱**（黄色）- 位于右上角，带浮动效果
- **八面体**（紫色）- 位于左下角，带旋转动画

### 动画效果
- 所有模型都有独立的旋转动画
- 浮动动画让模型上下移动
- 动画流畅，与地图操作同步

### 交互功能
- 支持地图缩放、旋转、倾斜操作
- 3D模型会随地图视角变化
- 可通过控制面板开关图层显示

## 🛠️ 技术实现

### 核心技术栈
- **THREE.js 0.142** - 3D渲染引擎
- **高德地图 GLCustomLayer** - 自定义图层API
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库

### 关键文件
```
src/
├── managers/
│   └── ThreeJSCustomLayer.js     # 3D图层管理器
├── view/
│   └── YunNanMap.vue             # 地图主组件
└── components/
    └── MapControlPanel.vue       # 控制面板
```

### 核心功能
1. **GLCustomLayer集成** - 与高德地图3D模式完美结合
2. **坐标转换** - 经纬度坐标自动转换为3D场景坐标
3. **相机同步** - THREE.js相机与地图相机实时同步
4. **动画系统** - 流畅的3D模型动画效果
5. **用户控制** - 通过UI面板控制图层显示

## 📋 功能特性

### ✅ 已实现功能
- [x] THREE.js 0.142版本集成
- [x] GLCustomLayer自定义图层创建
- [x] 5种不同3D几何体模型
- [x] 旋转和浮动动画效果
- [x] 光照系统（环境光+平行光）
- [x] 坐标转换和相机同步
- [x] 控制面板UI集成
- [x] 图层显示/隐藏控制
- [x] 3D模式自动检测

### 🔄 可扩展功能
- [ ] 加载外部3D模型文件（GLTF/OBJ）
- [ ] 更多动画效果和交互
- [ ] 模型点击事件处理
- [ ] 动态模型加载和卸载
- [ ] 性能优化和LOD系统
- [ ] 自定义材质和纹理

## 🎮 操作说明

### 地图操作
- **鼠标左键拖拽** - 平移地图
- **鼠标滚轮** - 缩放地图
- **鼠标右键拖拽** - 旋转地图（3D模式）
- **Shift + 鼠标拖拽** - 倾斜地图（3D模式）

### 控制面板
- **地图模式** - 2D/3D模式切换
- **地图图层** - 普通/卫星地图切换
- **地图显示** - 地名、边界、3D图层控制

## 🔧 开发说明

### 添加新的3D模型
在`ThreeJSCustomLayer.js`中的`createTestModels`方法里添加：

```javascript
// 创建新几何体
const geometry = new THREE.TorusGeometry(1000, 400, 16, 100);
const material = new THREE.MeshPhongMaterial({ color: 0x00ff00 });
const mesh = new THREE.Mesh(geometry, material);

// 设置位置
mesh.position.set(coord[0], coord[1], 1500);

// 添加到场景
this.scene.add(mesh);
this.meshes.push({ mesh, rotationSpeed: {...} });
```

### 自定义动画
修改`updateAnimations`方法：

```javascript
updateAnimations() {
  const time = Date.now() * 0.001;
  
  this.meshes.forEach((item) => {
    // 自定义动画逻辑
    item.mesh.rotation.x = time * 0.5;
    item.mesh.scale.setScalar(1 + Math.sin(time) * 0.2);
  });
}
```

## 🐛 故障排除

### 常见问题
1. **3D模型不显示** - 确保在3D模式下且3D图层开关已开启
2. **动画卡顿** - 检查模型数量和复杂度
3. **位置偏移** - 验证经纬度坐标和转换逻辑

### 调试方法
打开浏览器开发者工具查看控制台日志：
- 图层初始化状态
- 模型创建信息
- 动画更新状态
- 错误信息

## 📚 参考资料

- [高德地图GLCustomLayer官方示例](https://lbs.amap.com/demo/javascript-api-v2/example/selflayer/glcustom-layer)
- [THREE.js官方文档](https://threejs.org/docs/)
- [THREE.js 0.142版本说明](https://github.com/mrdoob/three.js/releases/tag/r142)

## 🎉 总结

THREE.js 3D自定义图层功能已成功集成到云南省地图项目中！现在你可以：

1. 在3D地图模式下查看精美的3D模型
2. 享受流畅的动画效果
3. 通过控制面板灵活控制图层显示
4. 基于现有框架轻松扩展更多3D功能

这个功能为项目提供了强大的3D可视化能力，可以用于展示各种空间数据、建筑模型、设备位置等，为用户提供更加直观和沉浸式的地图体验！
